import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'deepseek_api_service.dart';
import 'counselor_personalities.dart';
import 'safety_guidelines.dart';
import 'safety_service.dart';
import 'journal_service.dart';

class ChatMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'content': content,
    'isUser': isUser,
    'timestamp': timestamp.millisecondsSinceEpoch,
  };

  factory ChatMessage.fromJson(Map<String, dynamic> json) => ChatMessage(
    id: json['id'],
    content: json['content'],
    isUser: json['isUser'],
    timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
  );
}

class SessionProgress {
  final List<String> keyInsights;
  final List<String> goals;
  final List<String> achievements;
  final Map<String, dynamic> moodTracking;
  final DateTime lastUpdated;

  SessionProgress({
    required this.keyInsights,
    required this.goals,
    required this.achievements,
    required this.moodTracking,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() => {
    'keyInsights': keyInsights,
    'goals': goals,
    'achievements': achievements,
    'moodTracking': moodTracking,
    'lastUpdated': lastUpdated.millisecondsSinceEpoch,
  };

  factory SessionProgress.fromJson(Map<String, dynamic> json) =>
      SessionProgress(
        keyInsights: List<String>.from(json['keyInsights'] ?? []),
        goals: List<String>.from(json['goals'] ?? []),
        achievements: List<String>.from(json['achievements'] ?? []),
        moodTracking: Map<String, dynamic>.from(json['moodTracking'] ?? {}),
        lastUpdated: DateTime.fromMillisecondsSinceEpoch(json['lastUpdated']),
      );
}

enum SessionStatus { active, completed, paused }

enum SessionEndState {
  none,
  awaitingJournalingResponse,
  generatingPrompts,
  promptsGenerated,
  addingPromptsToJournal,
  promptsAddedToJournal,
  sessionEnding,
}

class CounselorSession {
  final String id;
  final String counselorName;
  final String counselorImage;
  final String counselorImagePath;
  final int counselorColor; // Store as int to avoid Color import issues
  final DateTime createdAt;
  final DateTime lastActive;
  final List<ChatMessage> messages;
  final SessionProgress progress;
  final SessionStatus status;
  final String? customTitle;
  final DateTime? completedAt;
  final List<String>? journalingPrompts;
  final SessionEndState endState;

  CounselorSession({
    required this.id,
    required this.counselorName,
    required this.counselorImage,
    required this.counselorImagePath,
    required this.counselorColor,
    required this.createdAt,
    required this.lastActive,
    required this.messages,
    required this.progress,
    this.status = SessionStatus.active,
    this.customTitle,
    this.completedAt,
    this.journalingPrompts,
    this.endState = SessionEndState.none,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'counselorName': counselorName,
    'counselorImage': counselorImage,
    'counselorImagePath': counselorImagePath,
    'counselorColor': counselorColor,
    'createdAt': createdAt.millisecondsSinceEpoch,
    'lastActive': lastActive.millisecondsSinceEpoch,
    'messages': messages.map((m) => m.toJson()).toList(),
    'progress': progress.toJson(),
    'status': status.toString(),
    'customTitle': customTitle,
    'completedAt': completedAt?.millisecondsSinceEpoch,
    'journalingPrompts': journalingPrompts,
    'endState': endState.toString(),
  };

  factory CounselorSession.fromJson(Map<String, dynamic> json) =>
      CounselorSession(
        id: json['id'],
        counselorName: json['counselorName'],
        counselorImage: json['counselorImage'],
        counselorImagePath: json['counselorImagePath'] ?? '',
        counselorColor: json['counselorColor'],
        createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
        lastActive: DateTime.fromMillisecondsSinceEpoch(json['lastActive']),
        messages:
            (json['messages'] as List)
                .map((m) => ChatMessage.fromJson(m))
                .toList(),
        progress: SessionProgress.fromJson(json['progress']),
        status:
            json['status'] != null
                ? SessionStatus.values.firstWhere(
                  (e) => e.toString() == json['status'],
                  orElse: () => SessionStatus.active,
                )
                : SessionStatus.active,
        customTitle: json['customTitle'],
        completedAt:
            json['completedAt'] != null
                ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
                : null,
        journalingPrompts:
            json['journalingPrompts'] != null
                ? List<String>.from(json['journalingPrompts'])
                : null,
        endState:
            json['endState'] != null
                ? SessionEndState.values.firstWhere(
                  (e) => e.toString() == json['endState'],
                  orElse: () => SessionEndState.none,
                )
                : SessionEndState.none,
      );

  String get lastMessage =>
      messages.isNotEmpty ? messages.last.content : 'Session started';
  int get messageCount => messages.length;

  /// Get the display title for this session
  String get displayTitle {
    if (customTitle != null && customTitle!.isNotEmpty) {
      return customTitle!;
    }

    // Generate title from first user message or session content
    final userMessages = messages.where((m) => m.isUser).toList();
    if (userMessages.isNotEmpty) {
      final firstMessage = userMessages.first.content;
      if (firstMessage.length > 50) {
        return '${firstMessage.substring(0, 47)}...';
      }
      return firstMessage;
    }

    return 'Session with $counselorName';
  }

  /// Check if session is completed (reached message limit or explicitly completed)
  bool get isCompleted =>
      status == SessionStatus.completed ||
      messages.length >= SessionService.maxMessagesPerSession;

  /// Check if session can be continued today
  bool get canContinueToday {
    if (status != SessionStatus.completed) return true;

    final now = DateTime.now();
    final completionDate = completedAt ?? lastActive;

    // Can continue if completed on a different day
    return completionDate.day != now.day ||
        completionDate.month != now.month ||
        completionDate.year != now.year;
  }

  /// Check if session has reached the 10-question limit for completion
  bool get hasReachedQuestionLimit {
    // Count counselor responses (questions)
    final counselorMessages = messages.where((m) => !m.isUser).toList();
    return counselorMessages.length >= 10;
  }

  /// Check if the last counselor message asks about journaling prompts
  bool get isAwaitingJournalingResponse {
    if (messages.isEmpty) return false;
    final lastMessage = messages.last;
    if (lastMessage.isUser) return false;

    final content = lastMessage.content.toLowerCase();
    return content.contains('journaling prompts') ||
        content.contains('journal prompts') ||
        (content.contains('journal') && content.contains('prompt'));
  }

  /// Create a copy of this session with updated fields
  CounselorSession copyWith({
    String? id,
    String? counselorName,
    String? counselorImage,
    String? counselorImagePath,
    int? counselorColor,
    DateTime? createdAt,
    DateTime? lastActive,
    List<ChatMessage>? messages,
    SessionProgress? progress,
    SessionStatus? status,
    String? customTitle,
    DateTime? completedAt,
    List<String>? journalingPrompts,
    SessionEndState? endState,
  }) {
    return CounselorSession(
      id: id ?? this.id,
      counselorName: counselorName ?? this.counselorName,
      counselorImage: counselorImage ?? this.counselorImage,
      counselorImagePath: counselorImagePath ?? this.counselorImagePath,
      counselorColor: counselorColor ?? this.counselorColor,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
      messages: messages ?? this.messages,
      progress: progress ?? this.progress,
      status: status ?? this.status,
      customTitle: customTitle ?? this.customTitle,
      completedAt: completedAt ?? this.completedAt,
      journalingPrompts: journalingPrompts ?? this.journalingPrompts,
      endState: endState ?? this.endState,
    );
  }
}

class SessionService {
  static const String _sessionsKey = 'counselor_sessions_v2';
  static const Uuid _uuid = Uuid();
  static const int maxSessionsPerDay = 3;
  static const int maxMessagesPerSession = 50; // Roughly 45-60 minutes

  /// Create a new counselor session
  static Future<CounselorSession> createSession({
    required String counselorName,
    required String counselorImage,
    required String counselorImagePath,
    required int counselorColor,
  }) async {
    // Check daily limits
    final exceedsLimit = await SafetyService.checkDailyLimits();
    if (exceedsLimit) {
      throw Exception(
        'Daily session limit reached. Please try again tomorrow or contact a licensed professional for immediate support.',
      );
    }

    final sessionId = _uuid.v4();
    final session = CounselorSession(
      id: sessionId,
      counselorName: counselorName,
      counselorImage: counselorImage,
      counselorImagePath: counselorImagePath,
      counselorColor: counselorColor,
      createdAt: DateTime.now(),
      lastActive: DateTime.now(),
      messages: [],
      progress: SessionProgress(
        keyInsights: [],
        goals: [],
        achievements: [],
        moodTracking: {},
        lastUpdated: DateTime.now(),
      ),
    );

    // Record session start for daily limits
    await SafetyService.recordSessionStart(sessionId);

    // Add system reminder message first
    final systemMessage = _getSystemReminderMessage();
    var updatedSession = session.copyWith(
      messages: [
        ...session.messages,
        ChatMessage(
          id: _uuid.v4(),
          content: systemMessage,
          isUser: false,
          timestamp: DateTime.now(),
        ),
      ],
    );

    // Don't add welcome message immediately - let the UI trigger it with typing animation
    await _saveSession(updatedSession);
    return updatedSession;
  }

  /// Add a message to a session and get AI response
  static Future<CounselorSession> addMessage(
    CounselorSession session,
    String content,
    bool isUser,
  ) async {
    // Check session limits
    if (session.messages.length >= maxMessagesPerSession) {
      final limitMessage = ChatMessage(
        id: _uuid.v4(),
        content:
            '''Our session has reached its recommended length for today. This helps ensure quality care and gives you time to process what we've discussed.

I encourage you to:
• Reflect on our conversation
• Practice any coping strategies we discussed
• Consider scheduling with a human therapist for ongoing support

You can start a new session tomorrow, or reach out to a licensed professional if you need immediate support.

Crisis resources are always available.
You can view support services on your dashboard.''',
        isUser: false,
        timestamp: DateTime.now(),
      );

      final updatedSession = CounselorSession(
        id: session.id,
        counselorName: session.counselorName,
        counselorImage: session.counselorImage,
        counselorImagePath: session.counselorImagePath,
        counselorColor: session.counselorColor,
        createdAt: session.createdAt,
        lastActive: DateTime.now(),
        messages: [...session.messages, limitMessage],
        progress: session.progress,
      );

      await _saveSession(updatedSession);
      return updatedSession;
    }

    // Safety check for user messages
    if (isUser) {
      // Check for crisis indicators
      if (SafetyGuidelines.isCrisisMessage(content)) {
        final crisisResponse = SafetyGuidelines.getCrisisResponse(content);

        // Log safety incident
        await SafetyService.logSafetyIncident(
          sessionId: session.id,
          counselorName: session.counselorName,
          userMessage: content,
          incidentType: 'crisis_message',
          response: crisisResponse,
        );

        final crisisMessage = ChatMessage(
          id: _uuid.v4(),
          content: crisisResponse,
          isUser: false,
          timestamp: DateTime.now(),
        );

        final updatedSession = CounselorSession(
          id: session.id,
          counselorName: session.counselorName,
          counselorImage: session.counselorImage,
          counselorImagePath: session.counselorImagePath,
          counselorColor: session.counselorColor,
          createdAt: session.createdAt,
          lastActive: DateTime.now(),
          messages: [
            ...session.messages,
            ChatMessage(
              id: _uuid.v4(),
              content: content,
              isUser: true,
              timestamp: DateTime.now(),
            ),
            crisisMessage,
          ],
          progress: session.progress,
        );

        await _saveSession(updatedSession);
        return updatedSession;
      }

      // Check for prohibited content
      if (SafetyGuidelines.containsProhibitedContent(content)) {
        final prohibitedResponse =
            SafetyGuidelines.getProhibitedTechniqueResponse('this technique');

        // Log safety incident
        await SafetyService.logSafetyIncident(
          sessionId: session.id,
          counselorName: session.counselorName,
          userMessage: content,
          incidentType: 'prohibited_content',
          response: prohibitedResponse,
        );

        final safetyMessage = ChatMessage(
          id: _uuid.v4(),
          content: prohibitedResponse,
          isUser: false,
          timestamp: DateTime.now(),
        );

        final updatedSession = CounselorSession(
          id: session.id,
          counselorName: session.counselorName,
          counselorImage: session.counselorImage,
          counselorImagePath: session.counselorImagePath,
          counselorColor: session.counselorColor,
          createdAt: session.createdAt,
          lastActive: DateTime.now(),
          messages: [
            ...session.messages,
            ChatMessage(
              id: _uuid.v4(),
              content: content,
              isUser: true,
              timestamp: DateTime.now(),
            ),
            safetyMessage,
          ],
          progress: session.progress,
        );

        await _saveSession(updatedSession);
        return updatedSession;
      }
    }

    // If user message, check if it's already in the session (to avoid duplication from UI)
    if (isUser) {
      // Check if the last message is already this user message
      final lastMessage =
          session.messages.isNotEmpty ? session.messages.last : null;
      if (lastMessage != null &&
          lastMessage.isUser &&
          lastMessage.content == content) {
        // Message already exists, just generate AI response
        final aiResponse = await _generateAIResponse(session);
        final aiMessage = ChatMessage(
          id: _uuid.v4(),
          content: aiResponse,
          isUser: false,
          timestamp: DateTime.now(),
        );

        final updatedSession = CounselorSession(
          id: session.id,
          counselorName: session.counselorName,
          counselorImage: session.counselorImage,
          counselorImagePath: session.counselorImagePath,
          counselorColor: session.counselorColor,
          createdAt: session.createdAt,
          lastActive: DateTime.now(),
          messages: [...session.messages, aiMessage],
          progress: session.progress,
          endState: session.endState,
        );

        // Check if the AI response asks about journaling prompts
        if (_isJournalingPromptQuestion(aiResponse)) {
          final finalSession = updatedSession.copyWith(
            endState: SessionEndState.awaitingJournalingResponse,
          );
          await _saveSession(finalSession);
          return finalSession;
        }

        await _saveSession(updatedSession);
        return updatedSession;
      }
    }

    // Add the message normally if it's not a duplicate
    final message = ChatMessage(
      id: _uuid.v4(),
      content: content,
      isUser: isUser,
      timestamp: DateTime.now(),
    );

    final updatedMessages = [...session.messages, message];

    CounselorSession updatedSession = CounselorSession(
      id: session.id,
      counselorName: session.counselorName,
      counselorImage: session.counselorImage,
      counselorImagePath: session.counselorImagePath,
      counselorColor: session.counselorColor,
      createdAt: session.createdAt,
      lastActive: DateTime.now(),
      messages: updatedMessages,
      progress: session.progress,
    );

    // If user message, generate AI response
    if (isUser) {
      final aiResponse = await _generateAIResponse(updatedSession);
      final aiMessage = ChatMessage(
        id: _uuid.v4(),
        content: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
      );

      updatedSession = CounselorSession(
        id: session.id,
        counselorName: session.counselorName,
        counselorImage: session.counselorImage,
        counselorImagePath: session.counselorImagePath,
        counselorColor: session.counselorColor,
        createdAt: session.createdAt,
        lastActive: DateTime.now(),
        messages: [...updatedMessages, aiMessage],
        progress: session.progress,
        endState: session.endState,
      );

      // Check if the AI response asks about journaling prompts
      if (_isJournalingPromptQuestion(aiResponse)) {
        updatedSession = updatedSession.copyWith(
          endState: SessionEndState.awaitingJournalingResponse,
        );
      }
    }

    await _saveSession(updatedSession);
    return updatedSession;
  }

  /// Generate initial welcome message for a counselor (called from UI)
  static Future<CounselorSession> generateInitialWelcome(
    CounselorSession session,
  ) async {
    final welcomeMessage = await _generateWelcomeMessage(session.counselorName);
    return await addMessage(session, welcomeMessage, false);
  }

  /// Generate welcome message for a counselor
  static Future<String> _generateWelcomeMessage(String counselorName) async {
    final personality = CounselorPersonalities.getPersonality(counselorName);

    // Add explicit word count enforcement for welcome messages
    final enhancedPersonality = '''
$personality

CRITICAL WORD COUNT REQUIREMENT FOR WELCOME MESSAGE:
Your welcome message must be EXACTLY 30-50 words. Count your words carefully before responding. This is a strict requirement that cannot be violated.

WELCOME MESSAGE STRUCTURE:
- Brief personality introduction
- Therapeutic style explanation
- Invitation to share
- Avoid generic "How are you feeling?" openings
- Use warm, conversational tone (never clinical or flowery)
''';

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(enhancedPersonality),
        DeepSeekApiService.createUserMessage(
          'Please provide a warm, welcoming first message to start our therapy session. Keep it exactly 30-50 words and true to your personality. This is the very first interaction. IMPORTANT: Only provide the actual message - no notes, analysis, or metadata.',
        ),
      ];

      final response = await DeepSeekApiService.sendMessage(
        messages: messages,
        temperature: 0.7,
        maxTokens: 100, // Reduced for welcome messages
      );

      // Clean the response and enforce word limits
      String cleanedResponse = _cleanAIResponse(response);
      cleanedResponse = _enforceWordLimit(cleanedResponse, 50);

      return cleanedResponse;
    } catch (e) {
      // Fallback welcome messages
      switch (counselorName) {
        case 'Dr. Sage':
          return "Welcome. I'm Dr. Sage, and I'm here to walk alongside you on your journey of self-discovery. Take a moment to breathe, and when you're ready, share what's on your mind.";
        case 'Luna':
          return "Hello. I'm Luna, and I'm really glad you decided to come talk today. Whatever brought you here is welcome. What's going on for you right now?";
        case 'Kai':
          return "Hey! I'm Kai. Good on you for taking this step - that's already progress. So what's going on? What would you like to work on?";
        case 'Theo':
          return "Hey, I'm Theo. I'm here to help you sort through whatever's on your mind. What's the situation you'd like to think through together?";
        case 'Dr. Elena':
          return "Hi, I'm Dr. Elena. I'm glad you're here - reaching out is actually a really important first step. What's been challenging you lately?";
        case 'Zuri':
          return "Hey there. I'm Zuri, and I want you to know you belong here exactly as you are. What's been on your heart?";
        default:
          return "Hello! I'm here to support you. What would you like to talk about today?";
      }
    }
  }

  /// Get system reminder message for session start
  static String _getSystemReminderMessage() {
    return 'Session Information';
  }

  /// Clean AI response to remove metadata and notes
  static String _cleanAIResponse(String response) {
    // Remove common AI metadata patterns
    String cleaned = response;

    // Remove DeepSeek tokenization artifacts - dollar signs and standalone numbers
    // These are common BPE tokenization artifacts in DeepSeek responses
    cleaned = cleaned.replaceAll(RegExp(r'\$+'), ''); // Remove all dollar signs
    cleaned = cleaned.replaceAll(
      RegExp(r'\b1+\b'),
      '',
    ); // Remove standalone 1's
    cleaned = cleaned.replaceAll(
      RegExp(r'\b\d+\b(?=\s*$)', multiLine: true),
      '',
    ); // Remove numbers at end of lines

    // Remove special tokens and encoding artifacts
    cleaned = cleaned.replaceAll(
      RegExp(r'<\|.*?\|>'),
      '',
    ); // Special tokens like <|endoftext|>
    cleaned = cleaned.replaceAll(
      RegExp(r'\[INST\]|\[/INST\]'),
      '',
    ); // Instruction tokens
    cleaned = cleaned.replaceAll(RegExp(r'<s>|</s>'), ''); // Start/end tokens
    cleaned = cleaned.replaceAll(RegExp(r'▁+'), ' '); // Unicode space tokens

    // Remove notes in parentheses like "(Note: This opening is 28 words...)"
    cleaned = cleaned.replaceAll(
      RegExp(r'\(Note:.*?\)', caseSensitive: false),
      '',
    );

    // Remove analysis comments like "*Note:" or "Note:"
    cleaned = cleaned.replaceAll(
      RegExp(r'\*?Note:.*?(?=\n|$)', caseSensitive: false),
      '',
    );

    // Remove word count metadata like "[28 words]" or "(28 words)"
    cleaned = cleaned.replaceAll(
      RegExp(r'[\[\(]\d+\s*words?[\]\)]', caseSensitive: false),
      '',
    );

    // Remove standalone numbers that might be word counts or metadata
    cleaned = cleaned.replaceAll(RegExp(r'^\d+$', multiLine: true), '');

    // Remove random numbers at the end of lines that look like metadata
    cleaned = cleaned.replaceAll(RegExp(r'\s+\d+\s*$', multiLine: true), '');

    // Remove asterisks used for emphasis in AI responses
    cleaned = cleaned.replaceAll(RegExp(r'\*([^*]+)\*'), r'$1');

    // Remove markdown-style formatting that might leak through
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s*'), ''); // Headers
    cleaned = cleaned.replaceAll(RegExp(r'`([^`]+)`'), r'$1'); // Code blocks

    // Remove any remaining metadata patterns like "Analysis:" or "Response:"
    cleaned = cleaned.replaceAll(
      RegExp(
        r'^(Analysis|Response|Output|Result):\s*',
        caseSensitive: false,
        multiLine: true,
      ),
      '',
    );

    // Remove common AI response prefixes that might leak through
    cleaned = cleaned.replaceAll(
      RegExp(
        r'^(Here.s|Here is|I.ll|Let me).*?:',
        caseSensitive: false,
        multiLine: true,
      ),
      '',
    );

    // Clean up extra whitespace and newlines
    cleaned = cleaned.replaceAll(RegExp(r'\n\s*\n+'), '\n\n');
    cleaned = cleaned.replaceAll(RegExp(r'^\s+|\s+$', multiLine: true), '');
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
    cleaned = cleaned.trim();

    return cleaned;
  }

  /// Enforce word count limits on AI responses
  static String _enforceWordLimit(String response, int maxWords) {
    final words = response.split(RegExp(r'\s+'));

    if (words.length <= maxWords) {
      return response;
    }

    // If response is too long, truncate to maxWords and ensure it ends properly
    final truncatedWords = words.take(maxWords).toList();
    String truncated = truncatedWords.join(' ');

    // If the last word doesn't end with punctuation, add a period
    if (!truncated.endsWith('.') &&
        !truncated.endsWith('?') &&
        !truncated.endsWith('!')) {
      // Remove the last word if it's incomplete and add a period
      final finalWords =
          truncatedWords.take(truncatedWords.length - 1).toList();
      truncated = '${finalWords.join(' ')}.';
    }

    return truncated;
  }

  /// Check if a message is asking about journaling prompts
  static bool _isJournalingPromptQuestion(String message) {
    // Look for a standardized end-of-session marker phrase
    return message.contains(
      "Would you like me to provide some journaling prompts based on our conversation today?",
    );
  }

  /// Generate AI response based on session context
  static Future<String> _generateAIResponse(CounselorSession session) async {
    final personality = CounselorPersonalities.getPersonality(
      session.counselorName,
    );

    // Add boundary reminder every 3-4 exchanges
    final userMessageCount = session.messages.where((m) => m.isUser).length;
    String boundaryReminder = '';
    if (userMessageCount % 4 == 0 && userMessageCount > 0) {
      boundaryReminder =
          '\n\nReminder: I\'m an AI counselor providing supportive conversation, not a replacement for professional mental health treatment. For serious concerns, please consider connecting with a licensed therapist.';
    }

    // Build conversation context (last 10 messages to manage token usage)
    final recentMessages =
        session.messages.length > 10
            ? session.messages.sublist(session.messages.length - 10)
            : session.messages;

    final conversationHistory =
        recentMessages.map((msg) {
          return msg.isUser
              ? DeepSeekApiService.createUserMessage(msg.content)
              : DeepSeekApiService.createAssistantMessage(msg.content);
        }).toList();

    // Add explicit word count enforcement to the system message
    final enhancedPersonality = '''
$personality

CRITICAL WORD COUNT REQUIREMENT:
Your response must be EXACTLY 20-60 words. Count your words carefully before responding. If your response is longer than 60 words, cut it down immediately. This is a strict requirement that cannot be violated.

RESPONSE STRUCTURE REMINDER:
- VALIDATE (25%): 1-2 sentences acknowledging their feelings
- ANALYZE (50%): 1-3 sentences providing insights or connections
- ADVISE (15%): Only if appropriate, brief practical guidance
- QUESTION (10%): ONE question only to deepen exploration

Keep responses conversational, warm, and direct. Never use clinical or flowery language.
''';

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(enhancedPersonality),
        ...conversationHistory,
      ];

      final response = await DeepSeekApiService.sendMessage(
        messages: messages,
        temperature: 0.7, // Reduced for more consistent adherence to guidelines
        maxTokens: 150, // Significantly reduced to enforce word limits
      );

      // Clean the response to remove any AI metadata
      String cleanedResponse = _cleanAIResponse(response);

      // Enforce word count limits post-processing
      cleanedResponse = _enforceWordLimit(cleanedResponse, 60);

      // Check if session should end (e.g., reached question limit)
      if (session.hasReachedQuestionLimit) {
        // Add standardized end-of-session marker
        return "$cleanedResponse$boundaryReminder\n\nWould you like me to provide some journaling prompts based on our conversation today?";
      }

      return cleanedResponse + boundaryReminder;
    } catch (e) {
      return "I apologize, but I'm having trouble connecting right now. Please try again in a moment, and I'll be here to support you.";
    }
  }

  /// Save session to local storage
  static Future<void> _saveSession(CounselorSession session) async {
    final prefs = await SharedPreferences.getInstance();
    final sessions = await getAllSessions();

    // Update existing session or add new one
    final index = sessions.indexWhere((s) => s.id == session.id);
    if (index >= 0) {
      sessions[index] = session;
    } else {
      sessions.add(session);
    }

    // Keep only the 5 most recent sessions
    sessions.sort((a, b) => b.lastActive.compareTo(a.lastActive));
    final sessionsToKeep = sessions.take(5).toList();

    final sessionsJson =
        sessionsToKeep.map((s) => json.encode(s.toJson())).toList();
    await prefs.setStringList(_sessionsKey, sessionsJson);
  }

  /// Get all saved sessions
  static Future<List<CounselorSession>> getAllSessions() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = prefs.getStringList(_sessionsKey) ?? [];

    return sessionsJson
        .map((sessionStr) => CounselorSession.fromJson(json.decode(sessionStr)))
        .toList();
  }

  /// Get a specific session by ID
  static Future<CounselorSession?> getSession(String sessionId) async {
    final sessions = await getAllSessions();
    try {
      return sessions.firstWhere((s) => s.id == sessionId);
    } catch (e) {
      return null;
    }
  }

  /// Delete a session
  static Future<void> deleteSession(String sessionId) async {
    final prefs = await SharedPreferences.getInstance();
    final sessions = await getAllSessions();
    sessions.removeWhere((s) => s.id == sessionId);

    final sessionsJson = sessions.map((s) => json.encode(s.toJson())).toList();
    await prefs.setStringList(_sessionsKey, sessionsJson);
  }

  /// Complete a session and generate journaling prompts
  static Future<CounselorSession> completeSession(
    CounselorSession session,
    List<String> journalingPrompts,
  ) async {
    final completedSession = session.copyWith(
      status: SessionStatus.completed,
      completedAt: DateTime.now(),
      journalingPrompts: journalingPrompts,
      lastActive: DateTime.now(),
    );

    await _saveSession(completedSession);
    return completedSession;
  }

  /// Rename a session
  static Future<CounselorSession> renameSession(
    String sessionId,
    String newTitle,
  ) async {
    final session = await getSession(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    final renamedSession = session.copyWith(
      customTitle: newTitle,
      lastActive: DateTime.now(),
    );

    await _saveSession(renamedSession);
    return renamedSession;
  }

  /// Pause a session (save and exit)
  static Future<CounselorSession> pauseSession(CounselorSession session) async {
    // Generate AI title if session doesn't have one and has meaningful content
    String? aiTitle;
    if (session.customTitle == null && session.messages.length > 2) {
      try {
        aiTitle = await _generateSessionTitle(session);
      } catch (e) {
        // Continue without title if generation fails
      }
    }

    final pausedSession = session.copyWith(
      status: SessionStatus.paused,
      lastActive: DateTime.now(),
      customTitle: aiTitle ?? session.customTitle,
    );

    await _saveSession(pausedSession);
    return pausedSession;
  }

  /// Resume a paused session
  static Future<CounselorSession> resumeSession(String sessionId) async {
    final session = await getSession(sessionId);
    if (session == null) {
      throw Exception('Session not found');
    }

    final resumedSession = session.copyWith(
      status: SessionStatus.active,
      lastActive: DateTime.now(),
    );

    await _saveSession(resumedSession);
    return resumedSession;
  }

  /// Handle user response to journaling prompts question
  static Future<CounselorSession> handleJournalingResponse(
    CounselorSession session,
    bool wantsPrompts,
  ) async {
    if (wantsPrompts) {
      // Update session state to generating prompts
      var updatedSession = session.copyWith(
        endState: SessionEndState.generatingPrompts,
      );
      await _saveSession(updatedSession);

      // Generate prompts via AI
      final prompts = await _generateJournalingPromptsViaAI(session);

      // Create AI message with prompts
      final promptsMessage = _formatPromptsMessage(prompts);
      updatedSession = await addMessage(updatedSession, promptsMessage, false);

      // Update session with prompts and new state
      updatedSession = updatedSession.copyWith(
        journalingPrompts: prompts,
        endState: SessionEndState.promptsGenerated,
      );

      await _saveSession(updatedSession);
      return updatedSession;
    } else {
      // Generate goodbye message
      final goodbyeMessage = await _generateGoodbyeMessage(
        session.counselorName,
      );
      var updatedSession = await addMessage(session, goodbyeMessage, false);

      // Update session state to ending
      updatedSession = updatedSession.copyWith(
        endState: SessionEndState.sessionEnding,
        status: SessionStatus.completed,
        completedAt: DateTime.now(),
      );

      await _saveSession(updatedSession);
      return updatedSession;
    }
  }

  /// Generate journaling prompts based on session content
  static List<String> generateJournalingPrompts(CounselorSession session) {
    // Extract key themes from the conversation
    final userMessages = session.messages.where((m) => m.isUser).toList();

    if (userMessages.isEmpty) {
      return _getDefaultPrompts(session.counselorName);
    }

    // Generate personalized prompts based on session content
    return [
      'Reflecting on our conversation today, what was the most important insight you gained?',
      'What emotions came up for you during our session, and how did they feel in your body?',
      'If you could give advice to someone facing a similar situation, what would you tell them?',
      'What is one small step you can take this week based on what we discussed?',
      'How has your perspective on this situation changed since we started talking?',
    ];
  }

  /// Get default prompts for each counselor
  static List<String> _getDefaultPrompts(String counselorName) {
    switch (counselorName) {
      case 'Dr. Sage':
        return [
          'What thoughts are you observing without judgment today?',
          'How can you find stillness in this moment?',
          'What wisdom is your inner voice sharing with you?',
          'What are you grateful for in this present moment?',
          'How can you practice self-compassion today?',
        ];
      case 'Luna':
        return [
          'What does your younger self need to hear right now?',
          'How can you show yourself kindness today?',
          'What feelings are asking for your attention?',
          'What would healing look like for you?',
          'How can you honor your journey today?',
        ];
      case 'Kai':
        return [
          'What action will you take today to move forward?',
          'What obstacles are you ready to overcome?',
          'How will you celebrate your progress?',
          'What strength did you discover in yourself today?',
          'What goal feels most important to focus on now?',
        ];
      case 'Theo':
        return [
          'What patterns are you noticing in your thoughts?',
          'How can you approach this situation more logically?',
          'What evidence supports your current perspective?',
          'What would an objective observer notice?',
          'How can you organize your thoughts more clearly?',
        ];
      case 'Dr. Elena':
        return [
          'What strategies have proven most effective for you?',
          'How can you apply what research shows about this topic?',
          'What specific techniques will you practice this week?',
          'How will you measure your progress?',
          'What does the science tell us about your situation?',
        ];
      case 'Zuri':
        return [
          'How does your identity influence your experience?',
          'What parts of yourself do you want to honor today?',
          'How can you create more belonging in your life?',
          'What would full self-acceptance look like?',
          'How can you celebrate your authentic self?',
        ];
      default:
        return [
          'What was most meaningful about our conversation today?',
          'How are you feeling right now?',
          'What would you like to remember from this session?',
          'What step will you take next?',
          'How can you be kind to yourself today?',
        ];
    }
  }

  /// Generate journaling prompts via AI based on session content
  static Future<List<String>> _generateJournalingPromptsViaAI(
    CounselorSession session,
  ) async {
    final personality = CounselorPersonalities.getPersonality(
      session.counselorName,
    );

    // Build conversation context for prompt generation
    final userMessages = session.messages.where((m) => m.isUser).toList();
    final conversationSummary =
        userMessages.isNotEmpty
            ? userMessages.map((m) => m.content).join('\n')
            : 'General session';

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(personality),
        DeepSeekApiService.createUserMessage(
          '''Based on our conversation today, please generate 3-5 specific journaling prompts that would help me reflect on what we discussed.

Conversation themes: $conversationSummary

Please provide ONLY the prompts as a numbered list, no additional text or explanations. Each prompt should be thoughtful and specific to our conversation.''',
        ),
      ];

      final response = await DeepSeekApiService.sendMessage(
        messages: messages,
        temperature: 0.7,
        maxTokens: 400,
      );

      // Parse the response into individual prompts
      final prompts = _parsePromptsFromResponse(response);
      return prompts.isNotEmpty
          ? prompts
          : _getDefaultPrompts(session.counselorName);
    } catch (e) {
      // Fallback to default prompts if AI generation fails
      return _getDefaultPrompts(session.counselorName);
    }
  }

  /// Parse prompts from AI response
  static List<String> _parsePromptsFromResponse(String response) {
    // First clean the response of tokenization artifacts
    String cleanedResponse = _cleanAIResponse(response);

    final lines = cleanedResponse.split('\n');
    final prompts = <String>[];

    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isEmpty) continue;

      // Remove numbering (1., 2., etc.) and clean up
      String cleaned = trimmed.replaceAll(RegExp(r'^\d+\.\s*'), '').trim();

      // Additional cleaning for prompts
      cleaned = cleaned.replaceAll(
        RegExp(r'^\$+'),
        '',
      ); // Remove leading dollar signs
      cleaned = cleaned.replaceAll(
        RegExp(r'\$+$'),
        '',
      ); // Remove trailing dollar signs
      cleaned = cleaned.replaceAll(
        RegExp(r'\b1+\b'),
        '',
      ); // Remove standalone 1's
      cleaned = cleaned.trim();

      if (cleaned.isNotEmpty &&
          !cleaned.startsWith('Based on') &&
          !cleaned.startsWith('Here are') &&
          cleaned.length > 10) {
        // Ensure meaningful length
        prompts.add(cleaned);
      }
    }

    return prompts;
  }

  /// Format prompts into a message for display
  static String _formatPromptsMessage(List<String> prompts) {
    final buffer = StringBuffer();
    buffer.writeln(
      'Here are some journaling prompts based on our conversation:',
    );
    buffer.writeln();

    for (int i = 0; i < prompts.length; i++) {
      buffer.writeln('${i + 1}. ${prompts[i]}');
      if (i < prompts.length - 1) buffer.writeln();
    }

    return buffer.toString();
  }

  /// Generate goodbye message when user declines prompts
  static Future<String> _generateGoodbyeMessage(String counselorName) async {
    final personality = CounselorPersonalities.getPersonality(counselorName);

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(personality),
        DeepSeekApiService.createUserMessage(
          'The user has declined journaling prompts. Please provide a warm, supportive goodbye message that acknowledges our session and encourages them. Keep it brief (2-3 sentences) and true to your personality. End with something like "we\'ll continue this later" or similar.',
        ),
      ];

      final response = await DeepSeekApiService.sendMessage(
        messages: messages,
        temperature: 0.8,
        maxTokens: 200,
      );

      return _cleanAIResponse(response);
    } catch (e) {
      // Fallback goodbye messages
      switch (counselorName) {
        case 'Dr. Sage':
          return "Thank you for sharing your thoughts with me today. Take time to reflect on what we've discussed, and remember that growth happens in the quiet moments between our conversations. We'll continue this journey together next time.";
        case 'Luna':
          return "I'm grateful you trusted me with your thoughts today. Be gentle with yourself as you process what we talked about. Your healing journey is uniquely yours, and we'll continue exploring it together soon.";
        case 'Kai':
          return "Great session today! You've got the tools and the strength to keep moving forward. Take action on what resonated with you, and we'll build on that progress next time.";
        case 'Theo':
          return "Thanks for thinking through these ideas with me today. Take some time to organize your thoughts about what we discussed. We'll continue analyzing and problem-solving together soon.";
        case 'Dr. Elena':
          return "Excellent work today. Remember to practice the strategies we discussed - consistency is key for lasting change. We'll check in on your progress and adjust our approach as needed next time.";
        case 'Zuri':
          return "Thank you for bringing your whole self to our conversation today. Honor the insights you've gained and remember that your journey matters. We'll continue supporting your growth together soon.";
        default:
          return "Thank you for our conversation today. Take care of yourself, and we'll continue this important work together next time.";
      }
    }
  }

  /// Add prompts to journal and update session state
  static Future<CounselorSession> addPromptsToJournal(
    CounselorSession session,
  ) async {
    if (session.journalingPrompts == null) {
      throw Exception('No journaling prompts available');
    }

    // Update session state to adding prompts
    var updatedSession = session.copyWith(
      endState: SessionEndState.addingPromptsToJournal,
    );
    await _saveSession(updatedSession);

    try {
      // Generate session overview
      final sessionOverview = await _generateSessionOverview(session);

      // Add prompts to journal with session overview
      await JournalService.createFromSessionPromptsWithOverview(
        sessionId: session.id,
        counselorName: session.counselorName,
        sessionTitle: session.displayTitle,
        prompts: session.journalingPrompts!,
        sessionOverview: sessionOverview,
      );

      // Update session state to prompts added
      updatedSession = updatedSession.copyWith(
        endState: SessionEndState.promptsAddedToJournal,
      );
      await _saveSession(updatedSession);

      return updatedSession;
    } catch (e) {
      // Revert state on error
      updatedSession = updatedSession.copyWith(
        endState: SessionEndState.promptsGenerated,
      );
      await _saveSession(updatedSession);
      rethrow;
    }
  }

  /// Generate AI session overview for journal
  static Future<String> _generateSessionOverview(
    CounselorSession session,
  ) async {
    final personality = CounselorPersonalities.getPersonality(
      session.counselorName,
    );

    // Build conversation context for overview generation
    final userMessages = session.messages.where((m) => m.isUser).toList();
    final conversationSummary =
        userMessages.isNotEmpty
            ? userMessages.map((m) => m.content).join('\n')
            : 'General session';

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(personality),
        DeepSeekApiService.createUserMessage(
          '''Based on our conversation today, please provide a brief session overview (2-3 paragraphs) that summarizes:
1. Key themes and topics discussed
2. Main insights or breakthroughs
3. Areas for continued reflection

Conversation content: $conversationSummary

Please write this as a professional but warm session summary that would be helpful for personal reflection. Keep it concise and focused on the most important elements of our conversation.''',
        ),
      ];

      final response = await DeepSeekApiService.sendMessage(
        messages: messages,
        temperature: 0.7,
        maxTokens: 500,
      );

      return _cleanAIResponse(response);
    } catch (e) {
      // Fallback overview if AI generation fails
      return '''Session Summary:
Today's conversation with ${session.counselorName} covered important personal topics and provided valuable insights for your continued growth and reflection.

Key themes from our discussion included personal challenges, coping strategies, and areas for development. The conversation provided a supportive space to explore your thoughts and feelings.

This session offers a foundation for continued self-reflection and personal growth. Consider revisiting the insights gained and how they might apply to your daily life.''';
    }
  }

  /// Complete session after prompts are handled
  static Future<CounselorSession> completeSessionAfterPrompts(
    CounselorSession session,
  ) async {
    // Generate AI title for the session
    final aiTitle = await _generateSessionTitle(session);

    final completedSession = session.copyWith(
      status: SessionStatus.completed,
      completedAt: DateTime.now(),
      endState: SessionEndState.none,
      customTitle: aiTitle,
    );

    await _saveSession(completedSession);
    return completedSession;
  }

  /// Generate AI session title (3-4 words)
  static Future<String> _generateSessionTitle(CounselorSession session) async {
    final personality = CounselorPersonalities.getPersonality(
      session.counselorName,
    );

    // Build conversation context for title generation
    final userMessages = session.messages.where((m) => m.isUser).toList();
    final conversationSummary =
        userMessages.isNotEmpty
            ? userMessages.take(3).map((m) => m.content).join('\n')
            : 'General session';

    try {
      final messages = [
        DeepSeekApiService.createSystemMessage(personality),
        DeepSeekApiService.createUserMessage(
          '''Based on our conversation today, please generate a very brief session title that captures the main theme in exactly 3-4 words.

Conversation themes: $conversationSummary

Examples of good titles:
- "Anxiety and Coping"
- "Work Stress Management"
- "Relationship Challenges"
- "Self-Care Planning"
- "Confidence Building"

Please provide ONLY the title (3-4 words), no additional text or explanations.''',
        ),
      ];

      final response = await DeepSeekApiService.sendMessage(
        messages: messages,
        temperature: 0.7,
        maxTokens: 50,
      );

      final cleanedTitle = _cleanAIResponse(response).trim();

      // Ensure title is not too long and is meaningful
      final words = cleanedTitle.split(' ');
      if (words.length >= 2 && words.length <= 5 && cleanedTitle.length <= 30) {
        return cleanedTitle;
      } else {
        // Fallback to first few words if AI response is too long
        return words.take(3).join(' ');
      }
    } catch (e) {
      // Fallback titles based on counselor
      switch (session.counselorName) {
        case 'Dr. Sage':
          return 'Mindful Reflection';
        case 'Luna':
          return 'Healing Journey';
        case 'Kai':
          return 'Action Planning';
        case 'Theo':
          return 'Logical Analysis';
        case 'Dr. Elena':
          return 'Evidence-Based Growth';
        case 'Zuri':
          return 'Identity Exploration';
        default:
          return 'Personal Growth';
      }
    }
  }
}
